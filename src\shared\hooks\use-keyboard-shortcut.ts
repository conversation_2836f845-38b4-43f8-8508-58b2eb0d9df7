import { useCallback, useEffect, useMemo, useRef } from "react";

type KeyCombination = {
	key: string;
	ctrl?: boolean;
	alt?: boolean;
	shift?: boolean;
	meta?: boolean;
};

type ShortcutHandler = (event: KeyboardEvent) => void;

interface ShortcutOptions {
	preventDefault?: boolean;
	ignoreInputs?: boolean;
	ignoreContentEditable?: boolean;
	onlyWhenFocused?: boolean;
	targetElement?: HTMLElement | null;
	disabled?: boolean;
	fallback?: ShortcutHandler;
}

interface UseKeyboardShortcutProps {
	combination: KeyCombination | KeyCombination[];
	handler: ShortcutHandler;
	options?: ShortcutOptions;
}

const defaultOptions: ShortcutOptions = {
	preventDefault: true,
	ignoreInputs: true,
	ignoreContentEditable: true,
	onlyWhenFocused: false,
	disabled: false,
};

const isInputElement = (element: Element | null): boolean => {
	if (!element) return false;

	const tagName = element.tagName.toLowerCase();

	if (tagName === "input" || tagName === "textarea" || tagName === "select") {
		return true;
	}

	if (element.getAttribute("contenteditable") === "true") {
		return true;
	}

	if (element.getAttribute("role") === "textbox") {
		return true;
	}

	if (element.closest('[role="textbox"], [contenteditable="true"], input, textarea, select')) {
		return true;
	}

	return false;
};

const matchesCombination = (event: KeyboardEvent, combination: KeyCombination): boolean => {
	const hasCtrl = combination.ctrl ?? false;
	const hasAlt = combination.alt ?? false;
	const hasShift = combination.shift ?? false;
	const hasMeta = combination.meta ?? false;

	return (
		event.key.toLowerCase() === combination.key.toLowerCase() &&
		event.ctrlKey === hasCtrl &&
		event.altKey === hasAlt &&
		event.shiftKey === hasShift &&
		event.metaKey === hasMeta
	);
};

export const useKeyboardShortcut = ({ combination, handler, options = defaultOptions }: UseKeyboardShortcutProps) => {
	const mergedOptions = useMemo(() => ({ ...defaultOptions, ...options }), [options]);
	const handlerRef = useRef(handler);
	const combinationRef = useRef(combination);
	const optionsRef = useRef(mergedOptions);

	useEffect(() => {
		handlerRef.current = handler;
		combinationRef.current = combination;
		optionsRef.current = mergedOptions;
	}, [handler, combination, mergedOptions]);

	const handleKeyDown = useCallback((event: KeyboardEvent) => {
		const target = event.target as Element;
		const options = optionsRef.current;
		const combinations = Array.isArray(combinationRef.current) ? combinationRef.current : [combinationRef.current];

		if (options.ignoreInputs) {
			const activeElement = document.activeElement;
			if (isInputElement(target) || isInputElement(activeElement)) {
				return;
			}
		}

		if (options.targetElement && !options.targetElement.contains(target as Node)) {
			return;
		}

		const matchedCombination = combinations.some(combo => matchesCombination(event, combo));

		if (!matchedCombination) {
			return;
		}

		if (options.disabled) {
			if (options.fallback) {
				options.fallback(event);
			}
			return;
		}

		if (options.preventDefault) {
			event.preventDefault();
		}

		handlerRef.current(event);
	}, []);

	useEffect(() => {
		document.addEventListener("keydown", handleKeyDown);
		return () => {
			document.removeEventListener("keydown", handleKeyDown);
		};
	}, [handleKeyDown]);
};
