import { But<PERSON> } from "@/shared/components/ui/button";
import { useKeyboardShortcut } from "@/shared/hooks/use-keyboard-shortcut";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { FileClock } from "lucide-react";
import { ConsultPendingModal } from ".";

export const PendingOrderConsultButton = () => {
	const { isOpen, closeModal, toggleModal } = useModal();

	useKeyboardShortcut({
		combination: { key: "p" },
		handler: () => {
			if (!isOpen) {
				toggleModal();
			}
		},
		options: {
			ignoreInputs: true,
			preventDefault: true,
		},
	});

	return (
		<>
			<Button onClick={toggleModal} className="bg-mainColor shadow-main px-2 gap-1 2xl:gap-2 flex text-white 2xl:px-3 2xl:py-1 rounded-[15px]">
				<FileClock size={18} />
				Consultar pendentes
				<span className="text-xs bg-white text-mainColor px-1 rounded">P</span>
			</Button>
			<ConsultPendingModal isOpen={isOpen} onClose={closeModal} />
		</>
	);
};
