// LinkCpf.tsx
import { openModalCpfAtom } from "@/modules/sales-panel/states/cpf-modal-open.state";
import { Button } from "@/shared/components/ui/button";
import { useKeyboardShortcut } from "@/shared/hooks/use-keyboard-shortcut";
import { useAtom } from "jotai";
import { FileText } from "lucide-react";
import { ModalCpf } from "./modal";

interface ILinkCpfProps {
	orderId: number;
}

export const LinkCpf = ({ orderId }: ILinkCpfProps) => {
	const [, setOpenModalCpf] = useAtom(openModalCpfAtom);

	const handleOpenModal = () => {
		setOpenModalCpf({ isOpen: true, type: "add" });
	};

	useKeyboardShortcut({
		combination: { key: "n" },
		handler: () => {
			if (orderId) {
				handleOpenModal();
			}
		},
		options: {
			ignoreInputs: true,
			preventDefault: true,
			disabled: !orderId,
		},
	});

	return (
		<>
			<Button
				disabled={!orderId}
				onClick={handleOpenModal}
				className="bg-mainColor shadow-[0_4px_4px_0_rgba(0,0,0,0.25)] gap-2 flex text-white px-3 py-1 rounded-[15px]"
			>
				<FileText size={18} />
				CPF na nota
				<span className="text-xs bg-white text-mainColor px-1 rounded">N</span>
			</Button>
			<ModalCpf orderId={orderId} />
		</>
	);
};
