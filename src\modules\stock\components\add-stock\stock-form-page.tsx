import { AnimatePresence, motion } from "framer-motion";
import { Loader2, PackagePlus, X } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";

import { CardLMPContainer } from "@/shared/components/custom/card";
import { But<PERSON> } from "@/shared/components/ui/button";

import { useCreateStockForm } from "../../hooks/stock/create-stock-form.hook";

import { useQueryClient } from "@tanstack/react-query";
import { StockForm } from "./stock-form";
import { XmlUploadSection } from "./uploader-section";

export const StockFormPage: React.FC = () => {
	const [showForm, setShowForm] = useState(false);
	const [dataEntryMode, setDataEntryMode] = useState<"manual" | "xml">("xml");
	const [isProcessingXml, setIsProcessingXml] = useState(false);
	const firstInputRef = useRef<HTMLInputElement>(null);
	const queryClient = useQueryClient();

	const handleShowForm = () => {
		setShowForm(true);
	};

	const handleCancel = () => {
		setShowForm(false);
		setIsProcessingXml(false);
		queryClient.invalidateQueries({ queryKey: ["xml-upload"] });
		queryClient.invalidateQueries({ queryKey: ["stock"] });
	};

	useEffect(() => {
		if (showForm && dataEntryMode === "manual" && firstInputRef.current) {
			setTimeout(() => {
				firstInputRef.current?.focus();
			}, 100);
		}
	}, [showForm, dataEntryMode]);

	return (
		<CardLMPContainer
			description="Adicione estoque manualmente ou envie um XML para automatizar"
			icon={<PackagePlus size={22} className="text-gray-500" />}
			title="Adicionar Estoque"
			actions={
				<div className="flex items-center gap-2">
					{!showForm ? (
						<Button
							className="
              hidden md:flex items-center gap-2 
              bg-mainColor text-white px-4 py-2 
              rounded-[10px] h-[45px] text-sm font-medium 
              hover:bg-mainColor/90 transition-colors shadow-sm
            "
							onClick={() => {
								setDataEntryMode("manual");
								setIsProcessingXml(false);
								handleShowForm();
							}}
						>
							<PackagePlus size={18} />
							Adicionar Manualmente
						</Button>
					) : (
						<Button
							className="hidden md:flex items-center gap-2 bg-red-400 text-white px-4 py-2 rounded-[10px] h-[45px] text-sm font-medium hover:bg-red-500/90 transition-colors shadow-sm"
							onClick={handleCancel}
						>
							<X size={18} />
							Cancelar
						</Button>
					)}

					{showForm && (
						<Button
							className="md:hidden flex items-center gap-2 
							bg-red-400 text-white px-4 py-2 w-full
							rounded-[10px] h-[45px] text-sm font-medium 
							hover:bg-red-500/90 transition-colors shadow-sm"
							onClick={handleCancel}
						>
							<X size={18} />
							Cancelar
						</Button>
					)}
				</div>
			}
		>
			<div className="flex flex-col gap-4">
				<XmlUploadSection
					showForm={showForm}
					reset={data => {
						if (showForm) {
							const wrapper = document.getElementById("stock-form-wrapper");
							if (wrapper) {
								const event = new CustomEvent("reset-form", { detail: data });
								wrapper.dispatchEvent(event);
							}
							// Aguarda um pouco para garantir que o formulário foi resetado antes de remover o loading
							setTimeout(() => {
								setIsProcessingXml(false);
							}, 300);
						}
					}}
					onFileSelected={() => {
						setDataEntryMode("xml");
						setIsProcessingXml(true);
						handleShowForm();
					}}
					onError={() => {
						setIsProcessingXml(false);
					}}
				/>

				{!showForm && (
					<div className="md:hidden flex justify-center">
						<Button
							className="flex items-center gap-2 
							bg-mainColor/10 text-mainColor px-6 py-2
							rounded-full h-[40px] text-sm font-medium 
							hover:bg-mainColor/20 transition-colors"
							onClick={() => {
								setDataEntryMode("manual");
								setIsProcessingXml(false);
								handleShowForm();
							}}
						>
							<PackagePlus size={18} />
							Entrada Manual
						</Button>
					</div>
				)}

				<AnimatePresence mode="wait">
					{showForm && isProcessingXml && (
						<motion.div
							key="xml-processing"
							initial={{ opacity: 0, height: 0 }}
							animate={{ opacity: 1, height: "auto" }}
							exit={{ opacity: 0, height: 0 }}
							transition={{ duration: 0.3 }}
							className="flex flex-col items-center justify-center py-8 md:py-12 px-4"
						>
							<div className="flex flex-col items-center gap-4 text-center max-w-sm mx-auto">
								<motion.div className="relative" initial={{ scale: 0.8 }} animate={{ scale: 1 }} transition={{ duration: 0.3 }}>
									<Loader2 className="h-10 w-10 md:h-12 md:w-12 text-mainColor animate-spin" />
									<div className="absolute inset-0 h-10 w-10 md:h-12 md:w-12 border-2 border-mainColor/20 rounded-full"></div>
								</motion.div>
								<motion.div
									className="space-y-2"
									initial={{ opacity: 0, y: 10 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ delay: 0.2, duration: 0.3 }}
								>
									<h3 className="text-lg md:text-xl font-medium text-gray-900">Processando XML</h3>
									<p className="text-sm md:text-base text-gray-600 leading-relaxed">
										Estamos analisando e organizando os dados do seu arquivo XML. Em instantes o formulário será preenchido
										automaticamente.
									</p>
								</motion.div>
								<motion.div
									className="flex space-x-1 mt-2"
									initial={{ opacity: 0 }}
									animate={{ opacity: 1 }}
									transition={{ delay: 0.4, duration: 0.3 }}
								>
									<div className="w-2 h-2 bg-mainColor rounded-full animate-pulse"></div>
									<div className="w-2 h-2 bg-mainColor rounded-full animate-pulse" style={{ animationDelay: "0.2s" }}></div>
									<div className="w-2 h-2 bg-mainColor rounded-full animate-pulse" style={{ animationDelay: "0.4s" }}></div>
								</motion.div>
							</div>
						</motion.div>
					)}
					{showForm && !isProcessingXml && (
						<motion.div
							key="stock-form"
							initial={{ opacity: 0, height: 0 }}
							animate={{ opacity: 1, height: "auto" }}
							exit={{ opacity: 0, height: 0 }}
							transition={{ duration: 0.3 }}
						>
							<StockFormWrapper formMode={dataEntryMode} firstInputRef={firstInputRef} onSuccess={() => setShowForm(false)} />
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</CardLMPContainer>
	);
};

const StockFormWrapper: React.FC<{
	formMode: "manual" | "xml";
	firstInputRef: React.RefObject<HTMLInputElement>;
	onSuccess?: () => void;
}> = ({ formMode, firstInputRef, onSuccess }) => {
	const { formMethods, inventoryFieldArray } = useCreateStockForm();

	React.useEffect(() => {
		const handleReset = (event: CustomEvent) => {
			formMethods.reset(event.detail);
		};

		const wrapper = document.getElementById("stock-form-wrapper");
		if (wrapper) {
			wrapper.addEventListener("reset-form", handleReset as EventListener);
		}

		return () => {
			if (wrapper) {
				wrapper.removeEventListener("reset-form", handleReset as EventListener);
			}
		};
	}, [formMethods]);

	return (
		<div id="stock-form-wrapper">
			<StockForm
				methodsForm={formMethods}
				formMode={formMode}
				inventoryFieldArray={inventoryFieldArray}
				firstInputRef={firstInputRef}
				onSuccess={onSuccess}
			/>
		</div>
	);
};
