@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	:root {
		--radius: 0.5rem;
	}
}

@layer utilities {
	.animate-in {
		animation: animate-in 0.5s ease-out;
	}

	.fade-in {
		animation: fade-in 0.6s ease-out;
	}
}

@keyframes animate-in {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes fade-in {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

body {
	font-family: "Montserrat", sans-serif;
	overflow-x: hidden;
}
/* 
Prevent horizontal scroll globally
html,
body {
	max-width: 100vw;
	overflow-x: hidden;
}

Prevent all containers from causing horizontal overflow */
* {
	box-sizing: border-box;
}

*:not(svg):not(path) {
	max-width: 100%;
}

/* Scrollbar refinado, mais claro e menor */
::-webkit-scrollbar {
	width: 2x;
	height: 6px;
	background: transparent;
}

::-webkit-scrollbar-track {
	background: #f6fcfe;
	border-radius: 10px;
}

::-webkit-scrollbar-thumb {
	background: linear-gradient(135deg, #b2ebf2 60%, #e0f7fa 100%);
	border-radius: 10px;
	border: 2px solid #f6fcfe;
	box-shadow: 0 1px 3px rgba(0, 188, 212, 0.05);
	transition: background 0.2s;
}

::-webkit-scrollbar-thumb:hover {
	background: linear-gradient(135deg, #4dd0e1 60%, #b2ebf2 100%);
}

* {
	scrollbar-width: thin;
	scrollbar-color: #b2ebf2 #f6fcfe;
}

.scrollbar-hide {
	scrollbar-width: none;
	-ms-overflow-style: none;
}
.scrollbar-hide::-webkit-scrollbar {
	display: none;
}

:focus-visible {
	outline: 2px solid #00bcd4;
	outline-offset: 2px;
	box-shadow: 0 0 0 3px #b2ebf2;
	transition:
		outline-color 0.2s,
		box-shadow 0.2s;
}

:focus {
	outline: none;
}
