import { orderId<PERSON>tom } from "@/modules/sales-panel/states/order-id.state";
import { orderInfoAtom } from "@/modules/sales-panel/states/order-info.state";
import { Separator } from "@/shared/components/ui/separator";
import { useAtomValue } from "jotai";
import { ShoppingCart } from "lucide-react";
import { TitleWithLine } from "../title";
import { FinishButton } from "./finish";
import { ShippingDiscountForm } from "./shipping-discount-form";

const formatCurrency = (value: string | number | undefined) => {
	if (!value) return "R$ 0,00";
	const number = typeof value === "string" ? parseFloat(value) : value;
	return new Intl.NumberFormat("pt-BR", {
		style: "currency",
		currency: "BRL",
	}).format(number);
};

interface IResumeCardProps {
	customClassName?: string;
}

export const ResumeCard = ({ customClassName = "" }: IResumeCardProps) => {
	const orderId = useAtomValue(orderIdAtom);
	const orderInfo = useAtomValue(orderInfoAtom);
	if (!orderId) {
		return (
			<div
				className={`
          w-full min-h-[12rem] p-6 rounded-2xl shadow-main
          bg-gradient-to-br from-white to-[#f0fcfc]
          flex flex-col
          ${customClassName}
        `}
			>
				<TitleWithLine title="Resumo" icon={ShoppingCart} />

				<div className="flex-1 flex flex-col justify-center items-center text-center">
					<div className="mb-3 p-3 rounded-full bg-gray-50 border border-gray-100">
						<ShoppingCart className="w-8 h-8 text-gray-400" />
					</div>

					<h3 className="text-lg font-semibold text-gray-700 mb-2">Nenhuma venda em andamento</h3>

					<p className="text-sm text-gray-500 max-w-xs leading-relaxed">Adicione produtos ao carrinho para visualizar o resumo</p>
				</div>
			</div>
		);
	}

	return (
		<div
			className={`
        flex flex-col w-full max-w-full min-w-0
        p-5 sm:p-7 bg-white rounded-[20px] shadow-main
        border border-gray-200 transition-all duration-300
        ${customClassName}
      `}
		>
			<TitleWithLine title="Resumo do Pedido" icon={ShoppingCart} />

			<div className="flex flex-col flex-grow">
				<div
					className="
            grid grid-cols-1 gap-y-3
            text-gray-700 text-sm sm:text-base
            lg:grid-cols-2 lg:gap-x-4
            3xl:grid-cols-1
            overflow-hidden
          "
				>
					<div className="flex justify-between">
						<span className="font-semibold">Itens</span>
						<span>{orderInfo?.items?.length ?? 0}</span>
					</div>

					<div className="flex justify-between">
						<span className="font-semibold">Subtotal</span>
						<span>{formatCurrency(orderInfo?.subtotal)}</span>
					</div>

					<div className="flex justify-between">
						<span className="font-semibold">Desconto</span>
						<span>{formatCurrency(orderInfo?.discount)}</span>
					</div>

					<div className="flex justify-between">
						<span className="font-semibold">Frete</span>
						<span>{formatCurrency(orderInfo?.shippingCost)}</span>
					</div>
				</div>

				<Separator className="my-4 bg-gray-300" />

				<div className="flex justify-between items-center font-bold text-lg sm:text-xl text-gray-800">
					<span>Total</span>
					<span className="text-mainColor">{orderInfo?.total}</span>
				</div>

				<div className="flex justify-end flex-col sm:flex-row gap-3 mt-5">
					<ShippingDiscountForm />
					<FinishButton />
				</div>
			</div>
		</div>
	);
};
